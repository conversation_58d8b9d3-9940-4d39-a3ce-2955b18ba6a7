# 股票持仓管理系统 - 开发规范

## 📝 代码编写规范

### 1. 文件和目录命名
- **所有文件和目录必须使用中文命名**
- **Python文件**: `功能描述.py` (如: `持仓管理器.py`)
- **HTML模板**: `页面名称.html` (如: `持仓列表.html`)
- **CSS样式**: `样式类型.css` (如: `主样式.css`)
- **JavaScript**: `功能模块.js` (如: `图表组件.js`)
- **配置文件**: `配置类型.json` (如: `策略配置.json`)

### 2. Python代码规范

#### 变量命名
```python
# ✅ 推荐：使用中文注释 + 英文变量名
stock_code = "000001"  # 股票代码
holding_quantity = 1000  # 持仓数量
current_price = 12.50  # 当前价格
profit_margin = 0.15  # 盈利比例

# ❌ 避免：纯英文注释或无注释
code = "000001"
qty = 1000
price = 12.50
margin = 0.15
```

#### 函数命名和注释
```python
def 计算持仓盈亏(股票代码: str, 持仓数量: int, 成本价: float, 当前价: float) -> dict:
    """
    计算单只股票的持仓盈亏情况
    
    参数:
        股票代码 (str): 6位股票代码
        持仓数量 (int): 持有股票数量
        成本价 (float): 买入成本价格
        当前价 (float): 当前市场价格
    
    返回:
        dict: 包含盈亏金额、盈亏比例等信息的字典
        {
            '盈亏金额': float,
            '盈亏比例': float,
            '当前市值': float,
            '成本总额': float
        }
    """
    成本总额 = 持仓数量 * 成本价
    当前市值 = 持仓数量 * 当前价
    盈亏金额 = 当前市值 - 成本总额
    盈亏比例 = (盈亏金额 / 成本总额) * 100 if 成本总额 > 0 else 0
    
    return {
        '盈亏金额': round(盈亏金额, 2),
        '盈亏比例': round(盈亏比例, 2),
        '当前市值': round(当前市值, 2),
        '成本总额': round(成本总额, 2)
    }
```

#### 类定义规范
```python
class 持仓管理器:
    """
    持仓信息管理类
    
    负责持仓数据的增删改查操作，包括：
    - 添加新持仓
    - 更新持仓信息
    - 删除持仓记录
    - 查询持仓列表
    """
    
    def __init__(self, 数据库连接):
        """
        初始化持仓管理器
        
        参数:
            数据库连接: 数据库连接对象
        """
        self.数据库 = 数据库连接
        self.日志器 = self._初始化日志器()
    
    def 添加持仓(self, 持仓信息: dict) -> bool:
        """
        添加新的持仓记录
        
        参数:
            持仓信息 (dict): 包含股票代码、数量、成本价等信息
        
        返回:
            bool: 添加成功返回True，失败返回False
        """
        try:
            # 验证持仓信息格式
            if not self._验证持仓信息(持仓信息):
                self.日志器.error(f"持仓信息格式错误: {持仓信息}")
                return False
            
            # 执行数据库插入操作
            插入结果 = self.数据库.插入持仓记录(持仓信息)
            
            if 插入结果:
                self.日志器.info(f"成功添加持仓: {持仓信息['股票代码']}")
                return True
            else:
                self.日志器.error(f"数据库插入失败: {持仓信息}")
                return False
                
        except Exception as 错误:
            self.日志器.error(f"添加持仓时发生异常: {错误}")
            return False
```

### 3. 错误处理规范

#### 异常处理模式
```python
def 获取股票价格(股票代码: str) -> dict:
    """
    获取股票实时价格信息
    
    参数:
        股票代码 (str): 6位股票代码
    
    返回:
        dict: 股票价格信息，失败时返回错误信息
    """
    try:
        # 构建API请求URL
        请求地址 = f"https://api.example.com/stock/{股票代码}"
        
        # 发送HTTP请求
        响应 = requests.get(请求地址, timeout=10)
        响应.raise_for_status()  # 检查HTTP状态码
        
        # 解析JSON数据
        数据 = 响应.json()
        
        # 验证数据完整性
        if not self._验证价格数据(数据):
            raise ValueError("价格数据格式不正确")
        
        return {
            '成功': True,
            '股票代码': 股票代码,
            '当前价': float(数据['price']),
            '涨跌幅': float(数据['change_pct']),
            '更新时间': 数据['update_time']
        }
        
    except requests.exceptions.Timeout:
        return {'成功': False, '错误': '请求超时，请稍后重试'}
    except requests.exceptions.ConnectionError:
        return {'成功': False, '错误': '网络连接失败'}
    except requests.exceptions.HTTPError as 错误:
        return {'成功': False, '错误': f'HTTP请求失败: {错误}'}
    except ValueError as 错误:
        return {'成功': False, '错误': f'数据格式错误: {错误}'}
    except Exception as 错误:
        return {'成功': False, '错误': f'未知错误: {错误}'}
```

### 4. 日志记录规范

#### 日志配置
```python
import logging
from datetime import datetime
import os

def 配置日志系统():
    """配置应用日志系统"""
    
    # 确保日志目录存在
    日志目录 = "临时文件/日志文件/应用日志"
    os.makedirs(日志目录, exist_ok=True)
    
    # 生成日志文件名
    今日日期 = datetime.now().strftime('%Y%m%d')
    日志文件 = f"{日志目录}/app_{今日日期}.log"
    错误日志文件 = f"{日志目录}/error_{今日日期}.log"
    
    # 配置日志格式
    日志格式 = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 配置主日志处理器
    文件处理器 = logging.FileHandler(日志文件, encoding='utf-8')
    文件处理器.setLevel(logging.INFO)
    文件处理器.setFormatter(日志格式)
    
    # 配置错误日志处理器
    错误处理器 = logging.FileHandler(错误日志文件, encoding='utf-8')
    错误处理器.setLevel(logging.ERROR)
    错误处理器.setFormatter(日志格式)
    
    # 配置控制台处理器
    控制台处理器 = logging.StreamHandler()
    控制台处理器.setLevel(logging.INFO)
    控制台处理器.setFormatter(日志格式)
    
    # 配置根日志器
    根日志器 = logging.getLogger()
    根日志器.setLevel(logging.INFO)
    根日志器.addHandler(文件处理器)
    根日志器.addHandler(错误处理器)
    根日志器.addHandler(控制台处理器)
    
    return 根日志器
```

#### 日志使用示例
```python
import logging

# 获取日志器
日志器 = logging.getLogger(__name__)

def 更新股票数据():
    """更新所有股票数据"""
    日志器.info("开始更新股票数据")
    
    try:
        股票列表 = 获取股票列表()
        日志器.info(f"获取到 {len(股票列表)} 只股票")
        
        成功计数 = 0
        失败计数 = 0
        
        for 股票代码 in 股票列表:
            try:
                价格信息 = 获取股票价格(股票代码)
                if 价格信息['成功']:
                    更新数据库(股票代码, 价格信息)
                    成功计数 += 1
                    日志器.debug(f"成功更新 {股票代码}: {价格信息['当前价']}")
                else:
                    失败计数 += 1
                    日志器.warning(f"获取 {股票代码} 价格失败: {价格信息['错误']}")
                    
            except Exception as 错误:
                失败计数 += 1
                日志器.error(f"更新 {股票代码} 时发生异常: {错误}")
        
        日志器.info(f"股票数据更新完成 - 成功: {成功计数}, 失败: {失败计数}")
        
    except Exception as 错误:
        日志器.error(f"更新股票数据时发生严重错误: {错误}")
        raise
```

### 5. 数据验证规范

#### 输入验证函数
```python
import re
from typing import Union

def 验证股票代码(股票代码: str) -> bool:
    """
    验证股票代码格式是否正确
    
    参数:
        股票代码 (str): 待验证的股票代码
    
    返回:
        bool: 格式正确返回True，否则返回False
    """
    if not isinstance(股票代码, str):
        return False
    
    # 股票代码必须是6位数字
    if not re.match(r'^\d{6}$', 股票代码):
        return False
    
    # 检查是否为有效的A股代码范围
    代码数字 = int(股票代码)
    
    # 沪市A股: 600000-699999
    # 深市A股: 000000-099999, 300000-399999
    if (600000 <= 代码数字 <= 699999 or  # 沪市
        000000 <= 代码数字 <= 99999 or   # 深市主板
        300000 <= 代码数字 <= 399999):   # 创业板
        return True
    
    return False

def 验证持仓数量(数量: Union[int, str]) -> bool:
    """
    验证持仓数量是否有效
    
    参数:
        数量: 持仓数量（整数或字符串）
    
    返回:
        bool: 有效返回True，否则返回False
    """
    try:
        数量值 = int(数量)
        # 持仓数量必须为正整数，且通常是100的倍数
        return 数量值 > 0 and 数量值 % 100 == 0
    except (ValueError, TypeError):
        return False

def 验证价格(价格: Union[float, str]) -> bool:
    """
    验证价格是否有效
    
    参数:
        价格: 股票价格（浮点数或字符串）
    
    返回:
        bool: 有效返回True，否则返回False
    """
    try:
        价格值 = float(价格)
        # 价格必须为正数，且不超过合理范围
        return 0 < 价格值 <= 10000
    except (ValueError, TypeError):
        return False
```

### 6. API接口规范

#### 响应格式标准
```python
from flask import jsonify
from typing import Any, Optional

def 创建API响应(成功: bool, 数据: Any = None, 消息: str = "", 错误代码: Optional[str] = None) -> dict:
    """
    创建标准化的API响应格式
    
    参数:
        成功 (bool): 操作是否成功
        数据 (Any): 返回的数据内容
        消息 (str): 响应消息
        错误代码 (str, optional): 错误代码
    
    返回:
        dict: 标准化的响应字典
    """
    响应 = {
        '成功': 成功,
        '时间戳': datetime.now().isoformat(),
        '消息': 消息
    }
    
    if 成功:
        响应['数据'] = 数据
    else:
        响应['错误'] = {
            '代码': 错误代码 or 'UNKNOWN_ERROR',
            '详情': 消息
        }
    
    return 响应

# API路由示例
@app.route('/api/持仓列表', methods=['GET'])
def 获取持仓列表():
    """获取用户持仓列表API"""
    try:
        # 从数据库获取持仓数据
        持仓数据 = 持仓管理器.获取所有持仓()
        
        # 计算实时盈亏
        for 持仓 in 持仓数据:
            盈亏信息 = 计算持仓盈亏(
                持仓['股票代码'],
                持仓['持仓数量'],
                持仓['成本价'],
                持仓['当前价']
            )
            持仓.update(盈亏信息)
        
        return jsonify(创建API响应(
            成功=True,
            数据=持仓数据,
            消息=f"成功获取 {len(持仓数据)} 条持仓记录"
        ))
        
    except Exception as 错误:
        日志器.error(f"获取持仓列表失败: {错误}")
        return jsonify(创建API响应(
            成功=False,
            消息="获取持仓列表失败",
            错误代码="GET_HOLDINGS_ERROR"
        )), 500
```

### 7. 前端代码规范

#### JavaScript命名和注释
```javascript
// ✅ 推荐：中文注释 + 英文变量名
const stockCode = '000001';  // 股票代码
const holdingQuantity = 1000;  // 持仓数量
const currentPrice = 12.50;  // 当前价格

/**
 * 计算持仓盈亏
 * @param {string} stockCode - 股票代码
 * @param {number} quantity - 持仓数量
 * @param {number} costPrice - 成本价
 * @param {number} currentPrice - 当前价
 * @returns {Object} 盈亏计算结果
 */
function calculateProfit(stockCode, quantity, costPrice, currentPrice) {
    const totalCost = quantity * costPrice;  // 总成本
    const currentValue = quantity * currentPrice;  // 当前市值
    const profitAmount = currentValue - totalCost;  // 盈亏金额
    const profitRate = (profitAmount / totalCost) * 100;  // 盈亏比例
    
    return {
        profitAmount: profitAmount.toFixed(2),
        profitRate: profitRate.toFixed(2),
        currentValue: currentValue.toFixed(2),
        totalCost: totalCost.toFixed(2)
    };
}
```

#### CSS样式规范
```css
/* 主要样式文件 - 使用中文注释 */

/* 全局样式设置 */
body {
    font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
}

/* 持仓列表容器 */
.holdings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 持仓表格样式 */
.holdings-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.holdings-table th,
.holdings-table td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid #eee;
}

/* 盈利状态颜色 */
.profit-positive {
    color: #e74c3c;  /* 盈利显示红色 */
    font-weight: bold;
}

.profit-negative {
    color: #27ae60;  /* 亏损显示绿色 */
    font-weight: bold;
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
    .holdings-container {
        padding: 10px;
        margin: 10px;
    }
    
    .holdings-table {
        font-size: 14px;
    }
    
    .holdings-table th,
    .holdings-table td {
        padding: 8px 4px;
    }
}
```

### 8. 测试规范

#### 单元测试示例
```python
import unittest
from unittest.mock import patch, MagicMock
from 永久文件.业务逻辑.持仓管理器 import 持仓管理器

class 测试持仓管理器(unittest.TestCase):
    """持仓管理器单元测试类"""
    
    def setUp(self):
        """测试前准备工作"""
        self.模拟数据库 = MagicMock()
        self.管理器 = 持仓管理器(self.模拟数据库)
    
    def test_添加有效持仓(self):
        """测试添加有效持仓记录"""
        # 准备测试数据
        持仓信息 = {
            '股票代码': '000001',
            '持仓数量': 1000,
            '成本价': 10.50,
            '股票名称': '平安银行'
        }
        
        # 模拟数据库操作成功
        self.模拟数据库.插入持仓记录.return_value = True
        
        # 执行测试
        结果 = self.管理器.添加持仓(持仓信息)
        
        # 验证结果
        self.assertTrue(结果)
        self.模拟数据库.插入持仓记录.assert_called_once_with(持仓信息)
    
    def test_添加无效股票代码(self):
        """测试添加无效股票代码的持仓"""
        # 准备无效测试数据
        无效持仓信息 = {
            '股票代码': '12345',  # 无效的5位代码
            '持仓数量': 1000,
            '成本价': 10.50
        }
        
        # 执行测试
        结果 = self.管理器.添加持仓(无效持仓信息)
        
        # 验证结果
        self.assertFalse(结果)
        self.模拟数据库.插入持仓记录.assert_not_called()
    
    @patch('永久文件.业务逻辑.持仓管理器.获取股票价格')
    def test_更新持仓价格(self, 模拟获取价格):
        """测试更新持仓价格功能"""
        # 模拟股价API返回
        模拟获取价格.return_value = {
            '成功': True,
            '当前价': 12.80,
            '涨跌幅': 2.5
        }
        
        # 执行测试
        结果 = self.管理器.更新持仓价格('000001')
        
        # 验证结果
        self.assertTrue(结果['成功'])
        self.assertEqual(结果['当前价'], 12.80)

if __name__ == '__main__':
    unittest.main()
```

## 📋 代码审查清单

### 提交前检查项目
- [ ] 所有文件和目录使用中文命名
- [ ] 代码包含完整的中文注释
- [ ] 函数和类有详细的文档字符串
- [ ] 异常处理覆盖所有可能的错误情况
- [ ] 输入数据经过验证
- [ ] 日志记录关键操作和错误
- [ ] API响应格式符合标准
- [ ] 前端代码有适当的注释
- [ ] 单元测试覆盖主要功能
- [ ] 代码格式化符合PEP8标准

### 性能优化检查
- [ ] 数据库查询使用索引
- [ ] 频繁访问的数据使用缓存
- [ ] 大量数据处理使用分页
- [ ] 静态资源启用压缩
- [ ] 图片资源优化大小
- [ ] JavaScript代码压缩
- [ ] CSS样式合并优化

### 安全检查
- [ ] 用户输入经过验证和过滤
- [ ] SQL查询使用参数化
- [ ] 文件上传限制类型和大小
- [ ] 敏感信息不在代码中硬编码
- [ ] API接口有适当的访问控制
- [ ] 错误信息不泄露敏感数据
- [ ] 日志文件不包含密码等敏感信息
