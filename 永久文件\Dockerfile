# 腾讯云CloudBase部署专用Dockerfile
FROM python:3.11-slim

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV FLASK_ENV=production

# 设置工作目录
WORKDIR /app

# 复制所有文件
COPY . /app/

# 使用腾讯云镜像源安装依赖
RUN pip install -i https://mirrors.cloud.tencent.com/pypi/simple/ --trusted-host mirrors.cloud.tencent.com Flask==2.3.3 requests==2.31.0 pandas==2.0.3 openpyxl==3.1.2

# 创建必要目录
RUN mkdir -p uploads cache logs

# 暴露端口
EXPOSE 8080

# 启动应用
CMD ["python", "main.py"]
