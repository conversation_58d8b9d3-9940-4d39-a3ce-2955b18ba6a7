#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯云CloudBase启动文件
====================
"""

import os
import sys

def main():
    try:
        print("🚀 启动持仓系统...")
        
        # 设置环境变量
        os.environ['FLASK_ENV'] = 'production'
        
        # 导入主应用
        from 持仓系统_简化版 import app
        
        # 获取端口
        port = int(os.environ.get('PORT', 8080))
        print(f"🌐 端口: {port}")
        
        # 启动应用
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            threaded=True
        )
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
