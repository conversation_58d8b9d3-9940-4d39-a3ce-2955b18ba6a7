# 股票持仓管理系统 - 项目结构说明

## 📁 目录结构设计

### 永久文件/ (核心代码和配置)
```
永久文件/
├── 核心代码/
│   ├── 主应用.py                    # 主Flask应用
│   ├── 数据模型/
│   │   ├── 持仓模型.py              # 持仓数据模型
│   │   ├── 股票模型.py              # 股票信息模型
│   │   └── 用户模型.py              # 用户管理模型
│   ├── 业务逻辑/
│   │   ├── 持仓管理器.py            # 持仓增删改查逻辑
│   │   ├── 股价获取器.py            # 股价数据获取
│   │   ├── 策略计算器.py            # 卖出策略计算
│   │   └── 盈亏计算器.py            # 盈亏统计计算
│   ├── API接口/
│   │   ├── 持仓接口.py              # 持仓相关API
│   │   ├── 股票接口.py              # 股票信息API
│   │   └── 统计接口.py              # 统计分析API
│   └── 工具模块/
│       ├── 数据验证器.py            # 数据验证工具
│       ├── 错误处理器.py            # 错误处理工具
│       └── 缓存管理器.py            # 缓存管理工具
├── 前端界面/
│   ├── 静态资源/
│   │   ├── 样式表/
│   │   │   ├── 主样式.css          # 主要样式文件
│   │   │   ├── 移动端样式.css      # 移动端适配样式
│   │   │   └── 主题样式.css        # 主题配色样式
│   │   ├── 脚本文件/
│   │   │   ├── 主脚本.js           # 主要JavaScript逻辑
│   │   │   ├── 图表组件.js         # 图表展示组件
│   │   │   └── 工具函数.js         # 通用工具函数
│   │   └── 图片资源/
│   │       ├── 图标/               # 系统图标
│   │       └── 背景/               # 背景图片
│   └── 页面模板/
│       ├── 基础模板.html           # 基础页面模板
│       ├── 持仓列表.html           # 持仓列表页面
│       ├── 添加持仓.html           # 添加持仓页面
│       └── 统计分析.html           # 统计分析页面
├── 数据库/
│   ├── 数据库配置.py               # 数据库连接配置
│   ├── 初始化脚本.sql             # 数据库初始化脚本
│   └── 持仓数据.db                # SQLite数据库文件
├── 配置文件/
│   ├── 应用配置.py                 # Flask应用配置
│   ├── 开发配置.py                 # 开发环境配置
│   ├── 生产配置.py                 # 生产环境配置
│   └── 策略配置.json              # 卖出策略配置
└── 文档/
    ├── API文档.md                  # API接口文档
    ├── 部署指南.md                 # 部署说明文档
    ├── 开发规范.md                 # 代码开发规范
    └── 用户手册.md                 # 用户使用手册
```

### 临时文件/ (日志、缓存、临时数据)
```
临时文件/
├── 日志文件/
│   ├── 应用日志/
│   │   ├── app_YYYYMMDD.log       # 应用运行日志
│   │   ├── error_YYYYMMDD.log     # 错误日志
│   │   └── access_YYYYMMDD.log    # 访问日志
│   ├── 数据更新日志/
│   │   ├── stock_update_YYYYMMDD.log  # 股价更新日志
│   │   └── strategy_YYYYMMDD.log      # 策略计算日志
│   └── 系统日志/
│       ├── system_YYYYMMDD.log    # 系统运行日志
│       └── performance_YYYYMMDD.log   # 性能监控日志
├── 缓存数据/
│   ├── 股价缓存/
│   │   ├── stock_cache_YYYYMMDD.json  # 股价数据缓存
│   │   └── yearly_low_cache.json      # 年内最低价缓存
│   ├── 计算缓存/
│   │   ├── strategy_cache.json        # 策略计算结果缓存
│   │   └── statistics_cache.json     # 统计数据缓存
│   └── 会话缓存/
│       └── session_data/              # 用户会话数据
├── 上传文件/
│   ├── 导入数据/
│   │   └── YYYYMMDD_HHMMSS_文件名.xlsx  # 用户上传的Excel文件
│   └── 备份文件/
│       └── backup_YYYYMMDD.json       # 数据备份文件
└── 临时处理/
    ├── 数据处理/
    │   └── temp_processing/           # 数据处理临时文件
    └── 图片生成/
        └── charts/                    # 临时生成的图表文件
```

## 🔧 自动清理机制

### 清理策略
1. **日志文件**: 保留30天，超过自动删除
2. **缓存数据**: 保留7天，过期自动清理
3. **上传文件**: 保留90天，定期清理
4. **临时处理文件**: 保留1天，每日清理

### 清理脚本
- 位置: `永久文件/工具模块/文件清理器.py`
- 执行频率: 每日凌晨2点自动执行
- 清理日志: 记录到 `临时文件/日志文件/系统日志/cleanup_YYYYMMDD.log`

## 📋 文件命名规范

### 通用规范
1. **所有文件和文件夹使用中文命名**
2. **代码文件使用`.py`扩展名**
3. **配置文件使用`.json`或`.py`扩展名**
4. **文档文件使用`.md`扩展名**
5. **数据文件使用`.db`、`.json`、`.xlsx`等扩展名**

### 特殊命名规则
1. **日志文件**: `类型_YYYYMMDD.log`
2. **缓存文件**: `数据类型_cache.json`
3. **备份文件**: `backup_YYYYMMDD_HHMMSS.json`
4. **上传文件**: `YYYYMMDD_HHMMSS_原文件名.扩展名`

## 🎯 核心功能模块

### 1. 持仓管理模块
- **功能**: 持仓信息的增删改查
- **文件**: `永久文件/业务逻辑/持仓管理器.py`
- **数据表**: holdings (持仓表)

### 2. 股价获取模块  
- **功能**: 实时股价数据获取和缓存
- **文件**: `永久文件/业务逻辑/股价获取器.py`
- **缓存**: `临时文件/缓存数据/股价缓存/`

### 3. 策略计算模块
- **功能**: 卖出策略计算和信号生成
- **文件**: `永久文件/业务逻辑/策略计算器.py`
- **配置**: `永久文件/配置文件/策略配置.json`

### 4. 盈亏统计模块
- **功能**: 持仓盈亏计算和统计分析
- **文件**: `永久文件/业务逻辑/盈亏计算器.py`
- **缓存**: `临时文件/缓存数据/计算缓存/`

## 🔄 数据流转说明

### 数据输入流程
1. 用户通过Web界面添加持仓信息
2. 数据经过验证器验证格式和有效性
3. 验证通过后存储到数据库
4. 触发股价获取和策略计算

### 数据更新流程  
1. 定时任务获取最新股价数据
2. 更新持仓市值和盈亏信息
3. 重新计算卖出策略信号
4. 缓存计算结果，记录更新日志

### 数据展示流程
1. 前端请求持仓数据API
2. 后端从数据库和缓存获取数据
3. 计算实时盈亏和策略信号
4. 返回JSON格式数据给前端展示

## 📊 技术架构说明

### 后端技术栈
- **Web框架**: Flask 2.3.3
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **数据处理**: Pandas 2.0.3
- **HTTP请求**: Requests 2.31.0
- **Excel处理**: OpenPyXL 3.1.2

### 前端技术栈
- **基础**: HTML5 + CSS3 + JavaScript ES6
- **图表库**: Chart.js (用于数据可视化)
- **UI框架**: Bootstrap 5 (响应式设计)
- **图标**: Font Awesome (图标字体)

### 部署架构
- **开发环境**: 本地Flask开发服务器
- **生产环境**: Gunicorn + Nginx
- **云平台**: 腾讯云CloudBase / 阿里云ECS
- **域名**: 支持自定义域名绑定

## 🛡️ 安全考虑

### 数据安全
1. **输入验证**: 所有用户输入都经过严格验证
2. **SQL注入防护**: 使用参数化查询
3. **XSS防护**: 输出数据进行HTML转义
4. **文件上传安全**: 限制文件类型和大小

### 访问控制
1. **会话管理**: 使用Flask-Session管理用户会话
2. **CSRF防护**: 使用CSRF令牌防护
3. **API限流**: 防止恶意请求攻击
4. **日志审计**: 记录所有关键操作日志

## 🔧 开发环境配置

### 环境要求
- Python 3.8+
- pip 包管理器
- Git 版本控制
- 代码编辑器 (推荐 VS Code)

### 安装步骤
1. 克隆项目到本地
2. 创建Python虚拟环境
3. 安装依赖包: `pip install -r requirements.txt`
4. 初始化数据库: `python 永久文件/数据库/初始化脚本.py`
5. 启动开发服务器: `python 永久文件/核心代码/主应用.py`

### 开发工具配置
- **代码格式化**: Black
- **代码检查**: Flake8
- **类型检查**: MyPy
- **测试框架**: Pytest
